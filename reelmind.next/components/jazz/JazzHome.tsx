"use client";

import { useState } from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { nanoid } from 'nanoid';
import { toast } from 'sonner';
import { useMutation } from '@tanstack/react-query';

// Jazz imports
import { createCanvas } from '@/lib/jazz/api/canvas';
import { ChatTextarea } from './chat/ChatTextarea';
import { CanvasList } from './home/<USER>';
import { HomeHeader } from './home/<USER>';
import { ScrollArea } from './ui/scroll-area';
import { useConfigs } from '@/lib/jazz/contexts/configs';
import { DEFAULT_SYSTEM_PROMPT } from '@/lib/jazz/constants';

export function JazzHome() {
  const { t } = useTranslation();
  const { setInitCanvas } = useConfigs();
  const [currentCanvasId, setCurrentCanvasId] = useState<string | null>(null);

  const { mutate: createCanvasMutation, isPending } = useMutation({
    mutationFn: createCanvas,
    onSuccess: (data) => {
      setInitCanvas(true);
      setCurrentCanvasId(data.id);
      // In Next.js, we'll handle navigation differently
      // For now, we'll just show the canvas in the same component
    },
    onError: (error) => {
      toast.error(t('common:messages.error'), {
        description: error.message,
      });
    },
  });

  // If we have a canvas ID, show the canvas view
  if (currentCanvasId) {
    return (
      <div className="h-full">
        {/* Canvas view will be implemented later */}
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-4">Canvas: {currentCanvasId}</h2>
            <button 
              onClick={() => setCurrentCanvasId(null)}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              Back to Home
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      <ScrollArea className="h-full">
        <HomeHeader />

        <div className="relative flex flex-col items-center justify-center h-fit min-h-[calc(100vh-460px)] pt-[60px] select-none">
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h1 className="text-5xl font-bold mb-2 mt-8 text-center">
              {t('home:title')}
            </h1>
          </motion.div>
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <p className="text-xl text-gray-500 mb-8 text-center">
              {t('home:subtitle')}
            </p>
          </motion.div>

          <ChatTextarea
            className="w-full max-w-xl"
            messages={[]}
            onSendMessages={(messages, configs) => {
              createCanvasMutation({
                name: t('home:newCanvas'),
                canvas_id: nanoid(),
                messages: messages,
                session_id: nanoid(),
                text_model: configs.textModel,
                image_model: configs.imageModel,
                system_prompt:
                  localStorage.getItem('system_prompt') ||
                  DEFAULT_SYSTEM_PROMPT,
              });
            }}
            pending={isPending}
          />
        </div>

        <CanvasList />
      </ScrollArea>
    </div>
  );
}
