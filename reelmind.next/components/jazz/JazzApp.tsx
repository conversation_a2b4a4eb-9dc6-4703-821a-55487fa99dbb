"use client";

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import { Toaster } from 'sonner';

// Jazz imports - we'll need to update these paths
import { JazzThemeProvider } from '@/lib/jazz/contexts/theme';
import { ConfigsProvider } from '@/lib/jazz/contexts/configs';
import { AuthProvider } from '@/lib/jazz/contexts/AuthContext';
import { JazzHome } from './JazzHome';
import { JazzSettingsDialog } from './settings/dialog';
import { JazzLoginDialog } from './auth/LoginDialog';
import { JazzUpdateNotificationDialog } from './common/UpdateNotificationDialog';

// Import Jazz CSS
import '@/styles/jazz/assets/style/App.css';
import '@/lib/jazz/i18n';

const queryClient = new QueryClient();

export function JazzApp() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  return (
    <JazzThemeProvider defaultTheme="system" storageKey="jazz-ui-theme">
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <ConfigsProvider>
            <div className="jazz-app-container h-full">
              <JazzHome />
              
              {/* Jazz Dialogs */}
              <JazzUpdateNotificationDialog />
              <JazzSettingsDialog />
              <JazzLoginDialog />
            </div>
          </ConfigsProvider>
        </AuthProvider>
      </QueryClientProvider>
      <Toaster position="bottom-center" richColors />
    </JazzThemeProvider>
  );
}
